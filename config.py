# -*- coding: utf-8 -*-
"""
碎玻璃溜子计算与建模软件 - 配置文件

定义软件的基本参数、材料属性和界面配置
"""

import os
from typing import Dict, Any

# 软件基本信息
APP_NAME = "碎玻璃溜子计算与建模软件"
APP_VERSION = "1.2"
APP_AUTHOR = "工程设计团队"
APP_EMAIL = "<EMAIL>"

# 文件路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, "data")
OUTPUT_DIR = os.path.join(BASE_DIR, "output")
TEMP_DIR = os.path.join(BASE_DIR, "temp")
LOG_DIR = os.path.join(BASE_DIR, "logs")

# 确保目录存在
for directory in [DATA_DIR, OUTPUT_DIR, TEMP_DIR, LOG_DIR]:
    os.makedirs(directory, exist_ok=True)

# 溜子参数范围配置
PARAMETER_RANGES = {
    "length": {"min": 500, "max": 5000, "default": 2000, "unit": "mm"},
    "inlet_width": {"min": 200, "max": 1000, "default": 600, "unit": "mm"},
    "outlet_width": {"min": 150, "max": 800, "default": 400, "unit": "mm"},
    "height": {"min": 300, "max": 1500, "default": 800, "unit": "mm"},
    "angle": {"min": 15, "max": 45, "default": 30, "unit": "°"},
    "thickness": {"min": 1.5, "max": 6.0, "default": 2.0, "unit": "mm"}
}

# 法兰参数配置
FLANGE_TYPES = {
    "angle_steel": {
        "name": "角钢法兰",
        "width_range": {"min": 20, "max": 100, "default": 50, "unit": "mm"},
        "thickness_range": {"min": 3, "max": 12, "default": 5, "unit": "mm"}
    },
    "flat_steel": {
        "name": "扁钢法兰",
        "width_range": {"min": 30, "max": 150, "default": 80, "unit": "mm"},
        "thickness_range": {"min": 5, "max": 20, "default": 10, "unit": "mm"}
    }
}

# 材料属性配置
MATERIAL_PROPERTIES = {
    "Q235": {
        "name": "Q235钢",
        "density": 7850,  # kg/m³
        "yield_strength": 235,  # MPa
        "tensile_strength": 375,  # MPa
        "elastic_modulus": 206000  # MPa
    },
    "Q345": {
        "name": "Q345钢",
        "density": 7850,  # kg/m³
        "yield_strength": 345,  # MPa
        "tensile_strength": 470,  # MPa
        "elastic_modulus": 206000  # MPa
    }
}

# 默认材料
DEFAULT_MATERIAL = "Q235"

# 3D可视化配置
VISUALIZATION_CONFIG = {
    "background_color": (0.2, 0.2, 0.2, 1.0),
    "model_color": (0.7, 0.7, 0.9, 1.0),
    "flange_color": (0.9, 0.7, 0.7, 1.0),
    "wireframe_color": (0.3, 0.3, 0.3, 1.0),
    "grid_color": (0.5, 0.5, 0.5, 0.3),
    "camera_distance": 3000,
    "fov": 45
}

# 界面配置 - 优化布局，减少空白，增大3D视图
UI_CONFIG = {
    "window_size": (1800, 1200),  # 增大默认窗口尺寸
    "min_window_size": (1600, 1000),  # 增大最小窗口尺寸
    "splitter_sizes": [250, 1300, 250],  # 给3D视图更多空间
    "left_panel_max_width": 280,  # 限制左侧面板宽度
    "right_panel_max_width": 300,  # 限制右侧面板宽度
    "opengl_min_size": (800, 600),  # 3D视图最小尺寸
    "opengl_min_height": 800,  # 3D视图最小高度
    "font_family": "Microsoft YaHei",
    "font_size": 9,
    "compact_button_height": 28,  # 紧凑按钮高度
    "title_height": 25,  # 标题固定高度
    "bottom_panel_height": 32  # 底部面板高度
}

# 导出格式配置
EXPORT_FORMATS = {
    "STL": {"extension": ".stl", "description": "STL格式 (3D打印)"},
    "OBJ": {"extension": ".obj", "description": "OBJ格式 (通用3D)"},
    "PLY": {"extension": ".ply", "description": "PLY格式 (点云)"}
}

# 计算精度配置
CALCULATION_CONFIG = {
    "mesh_resolution": 50,  # 网格分辨率
    "volume_precision": 6,  # 体积计算精度
    "area_precision": 4,   # 面积计算精度
    "weight_precision": 2  # 重量计算精度
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
    "rotation": "10 MB",
    "retention": "30 days"
}

# 获取配置函数
def get_config(key: str, default: Any = None) -> Any:
    """
    获取配置值

    Args:
        key: 配置键名
        default: 默认值

    Returns:
        配置值
    """
    config_map = {
        "parameter_ranges": PARAMETER_RANGES,
        "flange_types": FLANGE_TYPES,
        "material_properties": MATERIAL_PROPERTIES,
        "visualization": VISUALIZATION_CONFIG,
        "ui": UI_CONFIG,
        "export_formats": EXPORT_FORMATS,
        "calculation": CALCULATION_CONFIG,
        "log": LOG_CONFIG
    }
    return config_map.get(key, default)