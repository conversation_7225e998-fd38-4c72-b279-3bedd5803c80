# -*- coding: utf-8 -*-
"""
碎玻璃溜子计算与建模软件 - 核心计算模块

实现溜子的几何计算、重量计算和表面积计算
"""

import numpy as np
import math
from typing import Dict, Tuple, List, Optional
from dataclasses import dataclass
from config import MATERIAL_PROPERTIES, DEFAULT_MATERIAL, CALCULATION_CONFIG


@dataclass
class LiuZiParameters:
    """溜子参数数据类"""
    length: float  # 长度 (mm)
    inlet_width: float  # 入口宽度 (mm)
    outlet_width: float  # 出口宽度 (mm)
    height: float  # 高度 (mm)
    angle: float  # 倾斜角度 (度)
    thickness: float  # 板材厚度 (mm)
    flange_type: str  # 法兰类型
    flange_width: float  # 法兰宽度 (mm)
    flange_thickness: float  # 法兰厚度 (mm)
    material: str = DEFAULT_MATERIAL  # 材料类型


@dataclass
class CalculationResult:
    """计算结果数据类"""
    total_weight: float  # 总重量 (kg)
    surface_area: float  # 表面积 (m²)
    volume: float  # 体积 (m³)
    material_usage: Dict[str, float]  # 材料用量
    flange_info: Dict[str, any]  # 法兰信息
    geometry_info: Dict[str, float]  # 几何信息


class LiuZiCalculator:
    """溜子计算器类"""
    
    def __init__(self):
        self.precision = CALCULATION_CONFIG
    
    def calculate_all(self, params: LiuZiParameters) -> CalculationResult:
        """
        执行所有计算
        
        Args:
            params: 溜子参数
        
        Returns:
            计算结果
        """
        # 计算几何信息
        geometry_info = self._calculate_geometry(params)
        
        # 计算体积
        volume = self._calculate_volume(params, geometry_info)
        
        # 计算表面积
        surface_area = self._calculate_surface_area(params, geometry_info)
        
        # 计算重量
        total_weight = self._calculate_weight(params, volume, surface_area)
        
        # 计算材料用量
        material_usage = self._calculate_material_usage(params, surface_area)
        
        # 法兰信息
        flange_info = self._get_flange_info(params)
        
        return CalculationResult(
            total_weight=round(total_weight, self.precision["weight_precision"]),
            surface_area=round(surface_area, self.precision["area_precision"]),
            volume=round(volume, self.precision["volume_precision"]),
            material_usage=material_usage,
            flange_info=flange_info,
            geometry_info=geometry_info
        )
    
    def _calculate_geometry(self, params: LiuZiParameters) -> Dict[str, float]:
        """
        计算几何信息
        
        Args:
            params: 溜子参数
        
        Returns:
            几何信息字典
        """
        # 转换角度为弧度
        angle_rad = math.radians(params.angle)
        
        # 计算倾斜长度
        inclined_length = params.length / math.cos(angle_rad)
        
        # 计算垂直高度差
        height_diff = params.length * math.tan(angle_rad)
        
        # 计算侧面长度（梯形的斜边）
        width_diff = params.inlet_width - params.outlet_width
        side_length = math.sqrt(params.length**2 + width_diff**2)
        
        # 计算底面积（梯形）
        bottom_area = params.length * (params.inlet_width + params.outlet_width) / 2
        
        return {
            "inclined_length": inclined_length,
            "height_diff": height_diff,
            "side_length": side_length,
            "bottom_area": bottom_area,
            "angle_rad": angle_rad
        }
    
    def _calculate_volume(self, params: LiuZiParameters, geometry: Dict[str, float]) -> float:
        """
        计算溜子体积
        
        Args:
            params: 溜子参数
            geometry: 几何信息
        
        Returns:
            体积 (m³)
        """
        # 梯形截面的平均宽度
        avg_width = (params.inlet_width + params.outlet_width) / 2
        
        # 体积 = 底面积 × 高度
        volume_mm3 = geometry["bottom_area"] * params.height
        
        # 转换为立方米
        volume_m3 = volume_mm3 / (1000**3)
        
        return volume_m3
    
    def _calculate_surface_area(self, params: LiuZiParameters, geometry: Dict[str, float]) -> float:
        """
        计算表面积
        
        Args:
            params: 溜子参数
            geometry: 几何信息
        
        Returns:
            表面积 (m²)
        """
        # 底面积
        bottom_area = geometry["bottom_area"]
        
        # 两个侧面积（三角形）
        side_area = 2 * (0.5 * params.height * geometry["side_length"])
        
        # 入口面积（矩形）
        inlet_area = params.inlet_width * params.height
        
        # 出口面积（矩形）
        outlet_area = params.outlet_width * params.height
        
        # 总表面积
        total_area_mm2 = bottom_area + side_area + inlet_area + outlet_area
        
        # 转换为平方米
        total_area_m2 = total_area_mm2 / (1000**2)
        
        return total_area_m2
    
    def _calculate_weight(self, params: LiuZiParameters, volume: float, surface_area: float) -> float:
        """
        计算总重量
        
        Args:
            params: 溜子参数
            volume: 体积 (m³)
            surface_area: 表面积 (m²)
        
        Returns:
            总重量 (kg)
        """
        # 获取材料密度
        material_props = MATERIAL_PROPERTIES[params.material]
        density = material_props["density"]  # kg/m³
        
        # 板材厚度转换为米
        thickness_m = params.thickness / 1000
        
        # 板材重量 = 表面积 × 厚度 × 密度
        plate_weight = surface_area * thickness_m * density
        
        # 法兰重量
        flange_weight = self._calculate_flange_weight(params, density)
        
        return plate_weight + flange_weight
    
    def _calculate_flange_weight(self, params: LiuZiParameters, density: float) -> float:
        """
        计算法兰重量
        
        Args:
            params: 溜子参数
            density: 材料密度 (kg/m³)
        
        Returns:
            法兰重量 (kg)
        """
        # 法兰周长（入口和出口）
        inlet_perimeter = 2 * (params.inlet_width + params.height)
        outlet_perimeter = 2 * (params.outlet_width + params.height)
        total_perimeter = inlet_perimeter + outlet_perimeter
        
        # 法兰截面积
        if params.flange_type == "angle_steel":
            # 角钢截面积 = 2 * 边宽 * 厚度 - 厚度²
            cross_section = 2 * params.flange_width * params.flange_thickness - params.flange_thickness**2
        else:  # flat_steel
            # 扁钢截面积 = 宽度 * 厚度
            cross_section = params.flange_width * params.flange_thickness
        
        # 转换单位：mm² → m²
        cross_section_m2 = cross_section / (1000**2)
        total_perimeter_m = total_perimeter / 1000
        
        # 法兰重量 = 周长 × 截面积 × 密度
        flange_weight = total_perimeter_m * cross_section_m2 * density
        
        return flange_weight
    
    def _calculate_material_usage(self, params: LiuZiParameters, surface_area: float) -> Dict[str, float]:
        """
        计算材料用量
        
        Args:
            params: 溜子参数
            surface_area: 表面积 (m²)
        
        Returns:
            材料用量字典
        """
        # 板材用量（考虑10%损耗）
        plate_area = surface_area * 1.1
        
        # 法兰用量
        inlet_perimeter = 2 * (params.inlet_width + params.height) / 1000  # 转换为米
        outlet_perimeter = 2 * (params.outlet_width + params.height) / 1000
        total_flange_length = (inlet_perimeter + outlet_perimeter) * 1.05  # 5%损耗
        
        return {
            "plate_area_m2": round(plate_area, 3),
            "flange_length_m": round(total_flange_length, 2),
            "plate_thickness_mm": params.thickness,
            "material_type": params.material
        }
    
    def _get_flange_info(self, params: LiuZiParameters) -> Dict[str, any]:
        """
        获取法兰信息
        
        Args:
            params: 溜子参数
        
        Returns:
            法兰信息字典
        """
        if params.flange_type == "angle_steel":
            flange_spec = f"L{params.flange_width}×{params.flange_width}×{params.flange_thickness}"
            flange_name = "角钢法兰"
        else:
            flange_spec = f"{params.flange_width}×{params.flange_thickness}"
            flange_name = "扁钢法兰"
        
        return {
            "type": params.flange_type,
            "name": flange_name,
            "specification": flange_spec,
            "width": params.flange_width,
            "thickness": params.flange_thickness
        }
    
    def validate_parameters(self, params: LiuZiParameters) -> Tuple[bool, List[str]]:
        """
        验证参数有效性
        
        Args:
            params: 溜子参数
        
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        # 检查基本参数范围
        if not (500 <= params.length <= 5000):
            errors.append("长度必须在500-5000mm范围内")
        
        if not (200 <= params.inlet_width <= 1000):
            errors.append("入口宽度必须在200-1000mm范围内")
        
        if not (150 <= params.outlet_width <= 800):
            errors.append("出口宽度必须在150-800mm范围内")
        
        if not (300 <= params.height <= 1500):
            errors.append("高度必须在300-1500mm范围内")
        
        if not (15 <= params.angle <= 45):
            errors.append("倾斜角度必须在15-45度范围内")
        
        if not (1.5 <= params.thickness <= 6.0):
            errors.append("板材厚度必须在1.5-6.0mm范围内")
        
        # 检查逻辑关系
        if params.outlet_width >= params.inlet_width:
            errors.append("出口宽度必须小于入口宽度")
        
        # 检查材料
        if params.material not in MATERIAL_PROPERTIES:
            errors.append(f"不支持的材料类型: {params.material}")
        
        return len(errors) == 0, errors


# 便捷函数
def calculate_liuzi(params: LiuZiParameters) -> CalculationResult:
    """
    便捷的溜子计算函数
    
    Args:
        params: 溜子参数
    
    Returns:
        计算结果
    """
    calculator = LiuZiCalculator()
    return calculator.calculate_all(params)