# -*- coding: utf-8 -*-
"""
碎玻璃溜子计算与建模软件 - 3D建模模块

实现溜子的三维几何建模和网格生成
"""

import numpy as np
import trimesh
import math
from typing import List, Tuple, Dict, Optional
from calculator import LiuZiParameters
from config import CALCULATION_CONFIG


class LiuZi3DModeler:
    """溜子3D建模器类"""
    
    def __init__(self):
        self.mesh_resolution = CALCULATION_CONFIG["mesh_resolution"]
    
    def create_model(self, params: LiuZiParameters) -> trimesh.Trimesh:
        """
        创建完整的溜子3D模型
        
        Args:
            params: 溜子参数
        
        Returns:
            3D网格模型
        """
        # 创建主体模型
        main_body = self._create_main_body(params)
        
        # 创建法兰模型
        flanges = self._create_flanges(params)
        
        # 合并所有组件
        if flanges:
            combined_mesh = trimesh.util.concatenate([main_body] + flanges)
        else:
            combined_mesh = main_body
        
        # 修复网格
        combined_mesh.fix_normals()
        combined_mesh.remove_duplicate_faces()
        
        return combined_mesh
    
    def _create_main_body(self, params: LiuZiParameters) -> trimesh.Trimesh:
        """
        创建溜子主体
        
        Args:
            params: 溜子参数
        
        Returns:
            主体网格
        """
        # 计算关键点
        angle_rad = math.radians(params.angle)
        
        # 定义8个顶点（梯形截面的长方体）
        vertices = []
        
        # 入口端（z=0）
        inlet_half_width = params.inlet_width / 2
        vertices.extend([
            [-inlet_half_width, 0, 0],  # 左下
            [inlet_half_width, 0, 0],   # 右下
            [inlet_half_width, params.height, 0],  # 右上
            [-inlet_half_width, params.height, 0]  # 左上
        ])
        
        # 出口端（z=length，考虑倾斜）
        outlet_half_width = params.outlet_width / 2
        height_offset = params.length * math.tan(angle_rad)
        
        vertices.extend([
            [-outlet_half_width, -height_offset, params.length],  # 左下
            [outlet_half_width, -height_offset, params.length],   # 右下
            [outlet_half_width, params.height - height_offset, params.length],  # 右上
            [-outlet_half_width, params.height - height_offset, params.length]  # 左上
        ])
        
        vertices = np.array(vertices)
        
        # 定义面（使用右手法则）
        faces = [
            # 底面
            [0, 1, 5], [0, 5, 4],
            # 顶面
            [3, 7, 6], [3, 6, 2],
            # 左侧面
            [0, 4, 7], [0, 7, 3],
            # 右侧面
            [1, 2, 6], [1, 6, 5],
            # 入口面
            [0, 3, 2], [0, 2, 1],
            # 出口面
            [4, 5, 6], [4, 6, 7]
        ]
        
        # 创建网格
        mesh = trimesh.Trimesh(vertices=vertices, faces=faces)
        
        # 如果需要厚度，创建壳体
        if params.thickness > 0:
            mesh = self._create_shell(mesh, params.thickness)
        
        return mesh
    
    def _create_shell(self, mesh: trimesh.Trimesh, thickness: float) -> trimesh.Trimesh:
        """
        创建具有厚度的壳体
        
        Args:
            mesh: 原始网格
            thickness: 厚度 (mm)
        
        Returns:
            壳体网格
        """
        try:
            # 向内偏移创建内表面
            inner_mesh = mesh.copy()
            
            # 计算顶点法向量
            vertex_normals = mesh.vertex_normals
            
            # 向内偏移顶点
            inner_vertices = inner_mesh.vertices - vertex_normals * thickness
            inner_mesh.vertices = inner_vertices
            
            # 翻转内表面法向量
            inner_mesh.faces = inner_mesh.faces[:, ::-1]
            
            # 合并外表面和内表面
            combined_mesh = trimesh.util.concatenate([mesh, inner_mesh])
            
            return combined_mesh
        except:
            # 如果偏移失败，返回原始网格
            return mesh
    
    def _create_flanges(self, params: LiuZiParameters) -> List[trimesh.Trimesh]:
        """
        创建法兰
        
        Args:
            params: 溜子参数
        
        Returns:
            法兰网格列表
        """
        flanges = []
        
        # 入口法兰
        inlet_flange = self._create_single_flange(
            params, 
            position="inlet",
            width=params.inlet_width,
            height=params.height,
            z_offset=0
        )
        if inlet_flange:
            flanges.append(inlet_flange)
        
        # 出口法兰
        angle_rad = math.radians(params.angle)
        height_offset = params.length * math.tan(angle_rad)
        
        outlet_flange = self._create_single_flange(
            params,
            position="outlet",
            width=params.outlet_width,
            height=params.height,
            z_offset=params.length,
            height_offset=-height_offset
        )
        if outlet_flange:
            flanges.append(outlet_flange)
        
        return flanges
    
    def _create_single_flange(self, params: LiuZiParameters, position: str, 
                            width: float, height: float, z_offset: float,
                            height_offset: float = 0) -> Optional[trimesh.Trimesh]:
        """
        创建单个法兰
        
        Args:
            params: 溜子参数
            position: 位置（inlet/outlet）
            width: 开口宽度
            height: 开口高度
            z_offset: Z轴偏移
            height_offset: 高度偏移
        
        Returns:
            法兰网格
        """
        try:
            if params.flange_type == "angle_steel":
                return self._create_angle_steel_flange(
                    params, width, height, z_offset, height_offset
                )
            else:  # flat_steel
                return self._create_flat_steel_flange(
                    params, width, height, z_offset, height_offset
                )
        except Exception as e:
            print(f"创建法兰时出错: {e}")
            return None
    
    def _create_angle_steel_flange(self, params: LiuZiParameters, 
                                 width: float, height: float, 
                                 z_offset: float, height_offset: float) -> trimesh.Trimesh:
        """
        创建角钢法兰
        
        Args:
            params: 溜子参数
            width: 开口宽度
            height: 开口高度
            z_offset: Z轴偏移
            height_offset: 高度偏移
        
        Returns:
            角钢法兰网格
        """
        flange_meshes = []
        
        # 角钢参数
        leg_width = params.flange_width
        thickness = params.flange_thickness
        
        # 创建四条边的角钢
        edges = [
            # 底边
            {"start": [-width/2, height_offset, z_offset], 
             "end": [width/2, height_offset, z_offset], 
             "direction": [1, 0, 0]},
            # 顶边
            {"start": [-width/2, height + height_offset, z_offset], 
             "end": [width/2, height + height_offset, z_offset], 
             "direction": [1, 0, 0]},
            # 左边
            {"start": [-width/2, height_offset, z_offset], 
             "end": [-width/2, height + height_offset, z_offset], 
             "direction": [0, 1, 0]},
            # 右边
            {"start": [width/2, height_offset, z_offset], 
             "end": [width/2, height + height_offset, z_offset], 
             "direction": [0, 1, 0]}
        ]
        
        for edge in edges:
            angle_steel = self._create_angle_steel_segment(
                edge["start"], edge["end"], edge["direction"], 
                leg_width, thickness
            )
            if angle_steel:
                flange_meshes.append(angle_steel)
        
        if flange_meshes:
            return trimesh.util.concatenate(flange_meshes)
        else:
            return None
    
    def _create_angle_steel_segment(self, start: List[float], end: List[float], 
                                  direction: List[float], leg_width: float, 
                                  thickness: float) -> trimesh.Trimesh:
        """
        创建角钢段
        
        Args:
            start: 起点
            end: 终点
            direction: 方向向量
            leg_width: 角钢边宽
            thickness: 角钢厚度
        
        Returns:
            角钢段网格
        """
        # 计算长度
        length = np.linalg.norm(np.array(end) - np.array(start))
        
        # 创建L型截面的顶点
        cross_section = np.array([
            [0, 0], [leg_width, 0], [leg_width, thickness],
            [thickness, thickness], [thickness, leg_width], [0, leg_width]
        ])
        
        # 拉伸创建3D形状
        vertices_3d = []
        for i in range(2):  # 两个端面
            z = i * length
            for point in cross_section:
                if direction[0] != 0:  # X方向
                    vertices_3d.append([start[0] + i * (end[0] - start[0]), 
                                      start[1] + point[0], 
                                      start[2] + point[1]])
                else:  # Y方向
                    vertices_3d.append([start[0] + point[1], 
                                      start[1] + i * (end[1] - start[1]), 
                                      start[2] + point[0]])
        
        vertices_3d = np.array(vertices_3d)
        
        # 创建面
        faces = []
        n_points = len(cross_section)
        
        # 侧面
        for i in range(n_points):
            next_i = (i + 1) % n_points
            faces.extend([
                [i, next_i, next_i + n_points],
                [i, next_i + n_points, i + n_points]
            ])
        
        # 端面
        for i in range(1, n_points - 1):
            faces.append([0, i, i + 1])  # 第一个端面
            faces.append([n_points, n_points + i + 1, n_points + i])  # 第二个端面
        
        return trimesh.Trimesh(vertices=vertices_3d, faces=faces)
    
    def _create_flat_steel_flange(self, params: LiuZiParameters, 
                                width: float, height: float, 
                                z_offset: float, height_offset: float) -> trimesh.Trimesh:
        """
        创建扁钢法兰
        
        Args:
            params: 溜子参数
            width: 开口宽度
            height: 开口高度
            z_offset: Z轴偏移
            height_offset: 高度偏移
        
        Returns:
            扁钢法兰网格
        """
        flange_meshes = []
        
        # 扁钢参数
        steel_width = params.flange_width
        thickness = params.flange_thickness
        
        # 创建四条边的扁钢
        edges = [
            # 底边
            {"start": [-width/2 - steel_width/2, height_offset - steel_width/2, z_offset], 
             "length": width + steel_width, "direction": "horizontal"},
            # 顶边
            {"start": [-width/2 - steel_width/2, height + height_offset + steel_width/2, z_offset], 
             "length": width + steel_width, "direction": "horizontal"},
            # 左边
            {"start": [-width/2 - steel_width/2, height_offset + steel_width/2, z_offset], 
             "length": height - steel_width, "direction": "vertical"},
            # 右边
            {"start": [width/2 + steel_width/2, height_offset + steel_width/2, z_offset], 
             "length": height - steel_width, "direction": "vertical"}
        ]
        
        for edge in edges:
            flat_steel = self._create_flat_steel_segment(
                edge["start"], edge["length"], edge["direction"], 
                steel_width, thickness
            )
            if flat_steel:
                flange_meshes.append(flat_steel)
        
        if flange_meshes:
            return trimesh.util.concatenate(flange_meshes)
        else:
            return None
    
    def _create_flat_steel_segment(self, start: List[float], length: float, 
                                 direction: str, width: float, 
                                 thickness: float) -> trimesh.Trimesh:
        """
        创建扁钢段
        
        Args:
            start: 起点
            length: 长度
            direction: 方向（horizontal/vertical）
            width: 扁钢宽度
            thickness: 扁钢厚度
        
        Returns:
            扁钢段网格
        """
        if direction == "horizontal":
            # 水平扁钢
            vertices = np.array([
                # 底面
                [start[0], start[1], start[2]],
                [start[0] + length, start[1], start[2]],
                [start[0] + length, start[1] + width, start[2]],
                [start[0], start[1] + width, start[2]],
                # 顶面
                [start[0], start[1], start[2] + thickness],
                [start[0] + length, start[1], start[2] + thickness],
                [start[0] + length, start[1] + width, start[2] + thickness],
                [start[0], start[1] + width, start[2] + thickness]
            ])
        else:  # vertical
            # 垂直扁钢
            vertices = np.array([
                # 底面
                [start[0], start[1], start[2]],
                [start[0] + width, start[1], start[2]],
                [start[0] + width, start[1] + length, start[2]],
                [start[0], start[1] + length, start[2]],
                # 顶面
                [start[0], start[1], start[2] + thickness],
                [start[0] + width, start[1], start[2] + thickness],
                [start[0] + width, start[1] + length, start[2] + thickness],
                [start[0], start[1] + length, start[2] + thickness]
            ])
        
        # 定义面
        faces = [
            # 底面
            [0, 1, 2], [0, 2, 3],
            # 顶面
            [4, 7, 6], [4, 6, 5],
            # 侧面
            [0, 4, 5], [0, 5, 1],
            [1, 5, 6], [1, 6, 2],
            [2, 6, 7], [2, 7, 3],
            [3, 7, 4], [3, 4, 0]
        ]
        
        return trimesh.Trimesh(vertices=vertices, faces=faces)
    
    def export_model(self, mesh: trimesh.Trimesh, filepath: str, format: str = "STL") -> bool:
        """
        导出3D模型
        
        Args:
            mesh: 网格模型
            filepath: 文件路径
            format: 导出格式
        
        Returns:
            是否成功
        """
        try:
            if format.upper() == "STL":
                mesh.export(filepath)
            elif format.upper() == "OBJ":
                mesh.export(filepath)
            elif format.upper() == "PLY":
                mesh.export(filepath)
            else:
                return False
            return True
        except Exception as e:
            print(f"导出模型时出错: {e}")
            return False
    
    def get_model_info(self, mesh: trimesh.Trimesh) -> Dict[str, any]:
        """
        获取模型信息
        
        Args:
            mesh: 网格模型
        
        Returns:
            模型信息字典
        """
        return {
            "vertices_count": len(mesh.vertices),
            "faces_count": len(mesh.faces),
            "volume": mesh.volume if mesh.is_volume else 0,
            "surface_area": mesh.area,
            "bounds": mesh.bounds.tolist(),
            "center_mass": mesh.center_mass.tolist(),
            "is_watertight": mesh.is_watertight,
            "is_winding_consistent": mesh.is_winding_consistent
        }


# 便捷函数
def create_liuzi_model(params: LiuZiParameters) -> trimesh.Trimesh:
    """
    便捷的溜子建模函数
    
    Args:
        params: 溜子参数
    
    Returns:
        3D网格模型
    """
    modeler = LiuZi3DModeler()
    return modeler.create_model(params)